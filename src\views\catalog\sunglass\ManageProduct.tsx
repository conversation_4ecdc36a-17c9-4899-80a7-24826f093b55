/* eslint-disable */
import {
    Button,
    FormItem,
    toast,
    Select,
    Upload,
    FormContainer,
} from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { ChangeEvent, useEffect, useRef, useState } from 'react'
import Input from '@/components/ui/Input'
import { RichTextEditor } from '@/components/shared'
import { IoIosCloseCircleOutline } from 'react-icons/io'
import Checkbox from '@/components/ui/Checkbox'
import Radio from '@/components/ui/Radio/Radio'
import Breadcrumb from '@/views/modals/BreadCrumb'


const imageUrl = import.meta.env.VITE_ASSET_URL

const breadcrumbItems = [
    { title: 'Sunglass', url: '/catalog/sunglasses' },
    { title: 'Manage Product', url: '' },
]

export function sortPower(a: any, b: any) {
    if (a?.name?.[0] === "+" && b?.name?.[0] === "-") return 1;
    if (a?.name?.[0] === "-" && b?.name?.[0] === "+") return -1;
    if (a?.name?.[0] === b?.name?.[0] && (a?.name?.[0] === "+" || a?.name?.[0] === "-")) {
        if (Number(a?.name?.slice(1)) > Number(b?.name?.slice(1))) {
            // if(a?.name?.[0] === "+") return -1;
            // if(a?.name?.[0] === "-") return 1;  
            return 1;
        } else {
            // if(a?.name?.[0] === "+") return 1;
            // if(a?.name?.[0] === "-") return -1;
            return -1;
        }
    }
    if (a?.name?.[0] !== "+" && a?.name?.[0] !== "-") return -1
    if (b?.name?.[0] !== "-" && b?.name?.[0] !== "+") return 1
}

export default function ManageProduct({ res }: any) {
    const [brandOptions, setBrandOptions] = useState([])
    const [parentOptions, setParentOptions] = useState<any>([])
    const [isHeadLoading, setIsHeadLoading] = useState(true);
    const [productHeadValue, setProductHeadValue] = useState("");
    const [categoryOptions, setCategoryOptions] = useState([])
    const [productOptions, setProductOptions] = useState([])
    const [frameShapeOptions, setFrameShapeOptions] = useState([])
    const [frameTypeOptions, setFrameTypeOptions] = useState([])
    const [ageOptions, setAgeOptions] = useState([])
    const [sizeOptions, setSizeOptions] = useState([])
    const [colorOptions, setColorOptions] = useState<any>([])
    const [thumbnailFiles, setThumbnailFiles] = useState<any>([])
    const [variantFiles, setVariantFiles] = useState<any>([])
    const [thumbnailError, setThumbnailError] = useState<any>(null)
    const [variantError, setVariantError] = useState<any>(null)
    const [productFiles, setProductFiles] = useState<any>([])
    const [productFilesPreview, setProductFilesPreview] = useState<any>([])
    const [productError, setProductError] = useState<any>(null)
    const [descriptionFiles, setDescriptionFiles] = useState<any>([])
    const [descriptionError, setDescriptionError] = useState<any>(null)
    const [videoFile, setVideoFile] = useState<any>(null)
    const [videoPreview, setVideoPreview] = useState('')
    const [videoUrl, setVideoUrl] = useState<any>(null)
    const [productId, setProductId] = useState<any>(null)
    const [customizable, setCustomizable] = useState(false)
    const [isChooseLens, setIsChooseLens] = useState(false)
    const [isAddToCart, setIsAddToCart] = useState(false)
    // const [selectedType, setSelectedType] = useState('');
    const [isReturn, setIsReturn] = useState(false)
    const [labels, setLabels] = useState<any>([])
    const [frontMaterialOptions, setFrontMaterialOptions] = useState([])
    const [typeOptions, setTypeOptions] = useState([])
    const [lensMaterialOptions, setLensMaterialOptions] = useState([])
    const [variantColorOptions, setVariantColorOptions] = useState([])
    const [mainColor, setMainColor] = useState('')
    const [sphValues, setSphValues] = useState([])
    const [cylValues, setCylValues] = useState([])
    const [axisValues, setAxisValues] = useState([])
    const [addValues, setAddValues] = useState([])
    const [variantType, setVariantType] = useState('color')
    const [ogImageFile, setOgImageFile] = useState<string[]>([])
    const navigate = useNavigate()
    const params = useParams()
    const [searchParams, setSearchParams] = useSearchParams()
    const [loading, setLoading] = useState(false)
    const [product, setProduct] = useState<any>({})

    const [subCats, setSubCats] = useState<any>([])
    const [subChildCats, setSubChildCats] = useState<any>([])

    const ref = useRef<any>({})
    const [isShowPercentage, setIsShowPercentage] = useState(false)
    const [isTaxIncluded, setIsTaxIncluded] = useState(false)

    const genderOptions: any = [
        { value: 'Men', label: 'Men' },
        { value: 'Women', label: 'Women' },
        { value: 'Unisex', label: 'Unisex' },
        { value: 'Kids', label: 'Kids' },
    ]

    const handleThumbnailUpload = (files: any) => {
        setThumbnailFiles(files)
    }
    const handleVariantUpload = (files: any) => {
        setVariantFiles(files)
    }

    const handleProductUpload = (files: any) => {
        console.log(files)
        setProductFiles(files)
        setProductFilesPreview(files)
    }

    const handleDescriptionUpload = (files: any) => {
        setDescriptionFiles(files)
    }

    const onCheck = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setCustomizable(!customizable)
    }

    const onIsChooseLensCheck = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsChooseLens(!isChooseLens)
    }

    const onIsAddToCartCheck = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsAddToCart(!isAddToCart)
    }

    const onIsShowPercentage = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsShowPercentage(!isShowPercentage)
    }

    const onIsTaxIncluded = (checked: boolean, e: ChangeEvent<HTMLInputElement>) => {
        setIsTaxIncluded(!isTaxIncluded)
    }

    const handleOgImageUpload = (file: any) => {
        setOgImageFile(file)
    }

    const handleFileChange = (event: any) => {
        const file = event.target.files[0]
        const maxFileSize = 4 * 1024 * 1024
        if (file) {
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 4MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            setVideoFile(file)
            const videoURL = URL.createObjectURL(file)
            setVideoPreview(videoURL)
        }
    }

    const handleVideoUpload = () => {
        const formData = new FormData()
        formData.append('video', videoFile)
        api.post(endpoints.videoUpload, formData)
            .then((res) => {
                if (res?.status == 200) {
                    setVideoUrl(res?.data?.videoUrl)
                    setVideoFile(null)
                }
            })
            .catch((error) => {
                console.error('Error uploading video: ', error)
            })
    }

    const getSphValues = () => {
        api.get(endpoints.lensPowers + 'Sph').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.sort(sortPower).map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setSphValues(newPowers)
            }
        })
    }

    const getCylValues = () => {
        api.get(endpoints.lensPowers + 'Cyl').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.sort(sortPower).map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setCylValues(newPowers)
            }
        })
    }

    const getAxisValues = () => {
        api.get(endpoints.lensPowers + 'Axis').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setAxisValues(newPowers.sort((a: any, b: any) => a.label - b.label))
            }
        })
    }

    const getAddValues = () => {
        api.get(endpoints.lensPowers + 'Add').then((res) => {
            if (res.status == 200) {
                const powers = res?.data?.result || []
                const newPowers = powers.sort(sortPower).map(
                    (values: any) => ({
                        value: values._id,
                        label: values.name,
                    })
                )
                setAddValues(newPowers.sort((a: any, b: any) => a.label - b.label))
            }
        })
    }

    const getFrameShapes = () => {
        api.get(endpoints.activeFrameShape).then((res) => {
            if (res.status == 200) {
                const frameShapes = res?.data?.result || []
                const newFrameShapeOptions = frameShapes.map(
                    (frameShape: any) => ({
                        value: frameShape._id,
                        label: frameShape.name.en,
                    })
                )
                setFrameShapeOptions(newFrameShapeOptions)
            }
        })
    }

    const getFrameTypes = () => {
        api.get(endpoints.activeFrameType).then((res) => {
            if (res.status == 200) {
                const frameTypes = res?.data?.result || []
                const newFrameTypeOptions = frameTypes.map(
                    (frameType: any) => ({
                        value: frameType?._id,
                        label: frameType?.name?.en,
                    })
                )
                setFrameTypeOptions(newFrameTypeOptions)
            }
        })
    }

    const getColors = (variantColorOpts: any[] = []) => {
        api.post(endpoints.colors, { isActive: true }).then((res) => {
            if (res.status == 200) {
                const colors = res?.data?.result || []
                const newColorOptions = colors.map((color: any) => ({
                    value: color._id,
                    label: color.name.en,
                    isDisabled:
                        (params.type === 'Add-Variant' ||
                            params.type === 'Edit-Variant') &&
                        variantColorOpts.some(
                            (vc: any) => vc.value === color._id
                        ),
                }))
                setColorOptions(newColorOptions)
                console.log(newColorOptions)
            }
        })
    }

    const variantColors = () => {
        let main =
            params.type === 'Add-Variant'
                ? params.id
                : params.type == 'Edit-Variant'
                    ? params.main
                    : null
        console.log(' ---------------------', main)
        api.get(endpoints.variantColors + main).then((res) => {
            if (res?.status == 200) {
                const variantColors = res?.data?.result || []
                setMainColor(variantColors.mainColor.en)
                const newVariantColorOptions = variantColors.colors?.map(
                    (variantColor: any) => ({
                        value: variantColor?._id,
                        label: variantColor?.name?.en,
                    })
                )
                setVariantColorOptions(newVariantColorOptions)
                // Call getColors with variant color options only for variant pages
                if (
                    params.type === 'Add-Variant' ||
                    params.type === 'Edit-Variant'
                ) {
                    getColors(newVariantColorOptions)
                }
            }
        })
    }

    const getSizes = () => {
        api.post(endpoints.sizes, { isActive: true }).then((res) => {
            if (res.status == 200) {
                const sizes = res?.data?.result || []
                const newSizeOptions = sizes.map((size: any) => ({
                    value: size._id,
                    label: size.name,
                }))
                setSizeOptions(newSizeOptions)
            }
        })
    }

    const getBrands = () => {
        api.get(endpoints.brands)
            .then((res) => {
                if (res?.status == 200) {
                    const brands = res?.data?.result || []
                    const newBrandOptions = brands.map((brand: any) => ({
                        value: brand._id,
                        label: brand.name.en,
                    }))
                    setBrandOptions(newBrandOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    useEffect(() => {
        const controller = new AbortController()
        const limit = productHeadValue ? 60 : 30;
        setIsHeadLoading(true)
        api.post(endpoints.getProductHead + `?page=1&limit=${limit}`, { isActive: true, keyword: productHeadValue }, {
            signal: controller.signal
        }).then((res) => {
            if (res?.status == 200) {
                const productHeads = res?.data?.result?.productHeads || []
                let newproductOptions: any = productHeads.map((product: any) => ({
                    value: product.refid,
                    label: product.name,
                }))

                setParentOptions([...newproductOptions])
                if (searchParams.get("parent")) {
                    const head = newproductOptions.find((item: any) => item?.value == searchParams.get("parent"))
                    setValue('parent', head)
                }
            }
            setIsHeadLoading(false)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
            // setIsLoading(false)
        })
        return () => {
            controller.abort()
        }
    }, [productHeadValue])

    const getCategories = () => {
        api.get(endpoints.parentCategories)
            .then((res) => {
                if (res?.status == 200) {
                    const categories = res?.data?.result || []
                    const newCategoryOptions = categories.map(
                        (category: any) => ({
                            value: category._id,
                            label: category.name.en,
                            subCategory: category.subCategory
                        })
                    )
                    setCategoryOptions(newCategoryOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getProducts = () => {
        api.post(endpoints.products, { refid: params.id, isDefaultVariant: true })
            .then((res) => {
                if (res?.status == 200) {
                    const products = res?.data?.result?.products || []
                    const newproductOptions = products.map((product: any) => ({
                        value: product._id,
                        label: product.name.en,
                    }))
                    setProductOptions(newproductOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getAgeGroups = () => {
        api.post(endpoints.ageGroups, { isActive: true })
            .then((res) => {
                if (res?.status == 200) {
                    const ageGroups = res?.data?.result || []
                    const newAgeGroupOptions = ageGroups.map(
                        (ageGroup: any) => ({
                            value: ageGroup._id,
                            label: ageGroup.name,
                        })
                    )
                    setAgeOptions(newAgeGroupOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getLabels = () => {
        api.post(endpoints.labels, { isActive: true }).then((res) => {
            if (res?.status == 200) {
                const labels = res?.data?.result || []
                const newLabelOptions = labels.map((label: any) => ({
                    value: label._id,
                    label: label.name?.en,
                }))
                setLabels(newLabelOptions)
            }
        })
    }

    const getFrontMaterials = () => {
        api.get(endpoints.frontMaterials)
            .then((res) => {
                if (res?.status == 200) {
                    const frontMaterials = res?.data?.result || []
                    const activeFrontMaterials = frontMaterials.filter(
                        (frontMaterial: any) => frontMaterial.isActive
                    )
                    const newFrontMaterialOptions = activeFrontMaterials.map(
                        (frontMaterial: any) => ({
                            value: frontMaterial._id,
                            label: frontMaterial.name.en,
                        })
                    )
                    setFrontMaterialOptions(newFrontMaterialOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getTypes = () => {
        api.get(endpoints.types)
            .then((res) => {
                if (res?.status == 200) {
                    const types = res?.data?.result || []
                    const activeTypes = types.filter(
                        (type: any) => type.isActive
                    )
                    const newTypeOptions = activeTypes.map((type: any) => ({
                        value: type._id,
                        label: type.name.en,
                    }))
                    setTypeOptions(newTypeOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getLensMaterials = () => {
        api.get(endpoints.lensMaterials)
            .then((res) => {
                if (res?.status == 200) {
                    const lensMaterials = res?.data?.result || []
                    const activeLensMaterials = lensMaterials.filter(
                        (lensMaterial: any) => lensMaterial.isActive
                    )
                    const newLensMaterialOptions = activeLensMaterials.map(
                        (lensMaterial: any) => ({
                            value: lensMaterial._id,
                            label: lensMaterial.name.en,
                        })
                    )
                    setLensMaterialOptions(newLensMaterialOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }


    const onNameChange = (e: any) => {
        api.post("generate-slug", { name: e.target.value }).then(res => {
            setValue("slug", res.data.result.slug)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    useEffect(() => {
        const numberInputes = document.querySelectorAll('input[type=number]')
        numberInputes.forEach((input) => {
            input.addEventListener('mousewheel', function (e: any) {
                e.target?.blur()
            });
        });
    }, [])

    useEffect(() => {
        getBrands()
        getCategories()
        getProducts()
        getFrameShapes()
        getFrameTypes()
        getSizes()
        // getColors();
        // getAgeGroups()
        getLabels()
        getFrontMaterials()
        getTypes()
        getLensMaterials()
        getSphValues()
        getCylValues()
        getAxisValues()
        getAddValues()
        if (params.type === 'Add-Variant' || params.type === 'Edit-Variant') {
            variantColors() // This will call getColors after setting variant color options
        } else {
            getColors() // Call getColors without variant options for Add/Edit Product
        }

        if (sizeFields.length == 0) sizeAppend({})

        if (params.id) {

            api.get(endpoints.productDetail + params.id)
                .then((res) => {

                    if (res?.status == 200) {
                        const data = res?.data?.result
                        setProduct(data)
                        console.log(data)
                        setProductId(data?._id)
                        setValue('nameEn', data?.name?.en)
                        setValue('parent', {
                            value: data?.parent?.refid,
                            label: data?.parent?.name,
                        })
                        setValue('nameAr', data?.name?.ar)
                        setValue('weight', data?.weight)
                        setValue('position', data?.position)
                        setValue('modelName', data?.modelName)
                        setValue('modelCode', data?.modelCode)
                        setValue('colorCode', data?.colorCode)
                        setValue('brand', {
                            value: data?.brand?._id,
                            label: data?.brand?.name?.en,
                        })
                        if (data?.frameShape) setValue('frameShape', {
                            value: data?.frameShape?._id,
                            label: data?.frameShape?.name?.en,
                        })
                        if (data?.frameType) setValue('frameType', {
                            value: data?.frameType?._id,
                            label: data?.frameType?.name?.en,
                        })
                        setValue('gender', {
                            value: data?.gender,
                            label: data?.gender,
                        })
                        if (data?.category?.length > 0) {
                            setValue(
                                'category',
                                data?.category?.map((category: any) => ({
                                    value: category?._id,
                                    label: category?.name?.en,
                                    subCategory: category?.subCategory
                                }))
                            )

                        }

                        setValue('isCashbackEnabled', {
                            value: data?.isCashbackEnabled,
                            label: data?.isCashbackEnabled ? 'True' : 'False',
                        })
                        setValue('cashbackPercentage', data?.cashbackPercentage)

                        let subChilds: any = []
                        let third: any = []
                        let catIds = data?.category?.map((category: any) => category?._id)
                        data?.subCategory?.forEach((sub: any) => {
                            if (catIds.includes(sub?.parent)) {
                                subChilds.push({ value: sub?._id, label: sub?.name?.en })
                            }
                        })

                        if (subChilds.length > 0)
                            setValue('subCategory', subChilds?.map((category: any) => ({
                                value: category?.value,
                                label: category?.label,
                                childs: category?.childs
                            })));
                        let subIds = subChilds?.map((category: any) => category?.value)
                        data?.subCategory?.forEach((sub: any) => {
                            if (subIds.includes(sub?.parent)) {
                                third.push({ value: sub?._id, label: sub?.name?.en })
                            }
                        })
                        if (third?.length > 0)
                            setValue('subChildCategory', third?.map((category: any) => ({
                                value: category?.value,
                                label: category?.label,
                            })));
                        // if (data?.ageGroup.length > 0)
                        //     setValue(
                        //         'ageGroup',
                        //         data?.ageGroup?.map((ageGroup: any) => ({
                        //             value: ageGroup?._id,
                        //             label: ageGroup?.name,
                        //         }))
                        //     )
                        // setSelectedType(data?.productType);
                        // setValue('productType', { value: data?.productType, label: data?.productType });
                        if (data?.plans.length > 0) {
                            setValue(
                                'plans',
                                data?.plans?.map((plan: any) => ({
                                    value: plan?._id,
                                    label: plan?.name,
                                }))
                            )
                        }
                        setValue('sku', data?.sku?.trim())
                        if (data?.price?.aed) {
                            setValue('priceAed', data?.price?.aed)
                            setValue('offerPriceAed', data?.offerPrice?.aed)
                            setValue('stock', data?.stock)
                        }
                        setValue('slug', data?.slug)
                        setValue('descriptionEn', data?.description?.en)
                        setValue('descriptionAr', data?.description?.ar)
                        setValue('descriptionTwoEn', data?.descriptionTwo?.en ?? "")
                        setValue('descriptionTwoAr', data?.descriptionTwo?.ar ?? "")
                        setValue('sizeBridge', data?.sizeBridge ?? "")
                        setValue('lenseColor', data?.lenseColor ?? "")
                        setValue('templeLength', data?.templeLength ?? "")
                        if (data?.recommendedProducts?.length > 0)
                            setValue(
                                'recommended',
                                data?.recommendedProducts.map(
                                    (product: any) => ({
                                        value: product?._id,
                                        label: product?.name?.en,
                                    })
                                )
                            )
                        if (data?.boughtTogether.length > 0) setValue('boughtTogether', data?.boughtTogether.map((product: any) => ({
                            value: product?._id,
                            label: product?.name?.en
                        })));
                        if (data?.bundleOfferProduct) {
                            setValue('bundleOfferProduct', {
                                value: data?.bundleOfferProduct?._id,
                                label: data?.bundleOfferProduct?.name?.en
                            });
                        }
                        if (data?.bundleDiscountPrice) {
                            setValue('bundleDiscountPrice', data?.bundleDiscountPrice);
                        }
                        if (data?.technicalInfo.length > 0) {
                            setValue(
                                'technicalInfo',
                                data?.technicalInfo.map((info: any) => ({
                                    titleEn: info?.title?.en,
                                    titleAr: info?.title?.ar,
                                    contentEn: info?.description?.en,
                                    contentAr: info?.description?.ar,
                                }))
                            )
                        }
                        setValue('isActive', {
                            value: data?.isActive,
                            label: data?.isActive ? 'True' : 'False',
                        })
                        setValue('isNewArrival', {
                            value: data?.isNewArrival,
                            label: data?.isNewArrival ? 'True' : 'False',
                        })
                        // setValue('isReturnable', { value: data?.isReturnable, label: data?.isReturnable ? 'True' : 'False' })
                        // if (data?.isReturnable) {
                        //   setIsReturn(true)
                        //   setValue('returnDays', data?.returnDays)
                        // }
                        setValue('isVirtualTry', {
                            value: data?.isVirtualTry,
                            label: data?.isVirtualTry ? 'True' : 'False',
                        })
                        setValue('label', {
                            value: data?.label?._id,
                            label: data?.label?.name?.en,
                        })
                        setValue('isTryCart', {
                            value: data?.isTryCart,
                            label: data?.isTryCart ? 'True' : 'False',
                        })
                        if (data?.color) {
                            setValue('color', {
                                value: data?.color?._id,
                                label: data?.color?.name.en,
                            })
                        }
                        if (data?.size) {
                            setValue('size', {
                                value: data?.size?._id,
                                label: data?.size?.name,
                            })
                        }

                        setVariantType(data?.variantDisplayType ?? "color")
                        setIsTaxIncluded(data?.isTaxIncluded)
                        setIsChooseLens(data?.isChooseLens)
                        setIsAddToCart(data?.isAddToCart)
                        setIsShowPercentage(data?.showDiscountPercentage)
                        setThumbnailFiles([imageUrl + data?.thumbnail])
                        setVariantFiles(data?.variantImage ? [imageUrl + data?.variantImage] : [])
                        // setbannerFiles([imageUrl + data?.banner]);
                        setProductFiles(
                            data?.images?.map((image: any) => imageUrl + image)
                        )
                        setProductFilesPreview(
                            data?.images?.map((image: any) => imageUrl + image)
                        )
                        setDescriptionFiles(
                            data?.descriptionImages?.map(
                                (image: any) => imageUrl + image
                            )
                        )
                        setValue('supplierSku', data?.supplierSku)
                        setValue('upc', data?.upc)
                        if (data?.frontMaterial?.length > 0) setValue('frontMaterial',
                            data?.frontMaterial?.map((material: any) =>
                            ({
                                value: material?._id,
                                label: material?.name.en,
                            })
                            )
                        )
                        if (data?.lensMaterial?.length > 0) setValue('lensMaterial',
                            data?.lensMaterial?.map((material: any) =>
                            ({
                                value: material?._id,
                                label: material?.name.en,
                            })
                            )
                        )
                        if (data?.type?.length > 0) setValue('type',
                            data?.type?.map((type: any) => (
                                {
                                    value: type?._id,
                                    label: type?.name.en,
                                }
                            ))
                        )

                        if (data?.sphValues?.length > 0) setValue('sphValues',
                            data?.sphValues?.map((power: any) => (
                                {
                                    value: power?._id,
                                    label: power?.name,
                                }
                            ))
                        )
                        if (data?.cylValues?.length > 0) setValue('cylValues',
                            data?.cylValues?.map((power: any) => (
                                {
                                    value: power?._id,
                                    label: power?.name,
                                }
                            ))
                        )
                        if (data?.axisValues?.length > 0) setValue('axisValues',
                            data?.axisValues?.map((power: any) => (
                                {
                                    value: power?._id,
                                    label: power?.name,
                                }
                            ))
                        )
                        if (data?.addValues?.length > 0) setValue('addValues',
                            data?.addValues?.map((power: any) => (
                                {
                                    value: power?._id,
                                    label: power?.name,
                                }
                            ))
                        )

                        setValue('metaTitleEn', data?.seoDetails?.title?.en ?? "")
                        setValue('metaTitleAr', data?.seoDetails?.title?.ar ?? "")
                        setValue('metaDescriptionEn', data?.seoDetails?.description?.en ?? "")
                        setValue('metaDescriptionAr', data?.seoDetails?.description?.ar ?? "")
                        setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en ?? "")
                        setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar ?? "")
                        setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en ?? "")
                        setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar ?? "")
                        setOgImageFile([imageUrl + data?.seoDetails?.ogImage])
                    }

                }).catch((error) => {
                    if (error?.response?.status == 422) navigate('/access-denied');
                    console.error('Error fetching data: ', error)
                })

        } else {
            if (searchParams.get("parent")) {
                setProductHeadValue(searchParams.get("parent") as string)
                setValue('parent', {
                    value: searchParams.get("parent"),
                    label: searchParams.get("parent"),
                })
            }
        }
    }, [res])

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    // const productTypeOptions: any = [
    //   { value: "frame", label: "Frame" },
    //   { value: "contactLens", label: "Contact Lens" },
    // ]

    const {
        handleSubmit,
        control,
        setValue,
        watch,
        formState: { errors },
    } = useForm<any>()

    const watchCats = watch('category')
    const watchSubCats = watch('subCategory')

    useEffect(() => {
        const pop = async () => {
            if (watchSubCats?.length > 0) {
                const data: any = []
                for (let cat of watchSubCats) {
                    if (cat?.value in ref.current) {
                        data.push(...ref.current[cat?.value])
                    } else {
                        const res = await api.get(endpoints.getChildCategories + cat?.value)
                        res.data?.result?.forEach((item: any) => {
                            data.push({ label: item?.name?.en, value: item?._id })
                        })
                        ref.current[cat?.value] = data
                    }
                }
                setSubChildCats(data)
            } else setSubChildCats([])
        }
        pop()
    }, [watchSubCats])

    useEffect(() => {
        const pop = async () => {
            if (watchCats?.length > 0) {
                const data: any = []
                for (let cat of watchCats) {
                    if (cat?.value in ref.current) {
                        data.push(...ref.current[cat?.value])
                    } else {
                        const res = await api.get(endpoints.getChildCategories + cat?.value)
                        res.data?.result?.forEach((item: any) => {
                            data.push({ label: item?.name?.en, value: item?._id })
                        })
                        ref.current[cat?.value] = data
                    }
                }
                setSubCats(data)
            } else setSubCats([])
        }
        pop()
    }, [watchCats])

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'technicalInfo',
    })

    const {
        fields: sizeFields,
        append: sizeAppend,
        remove: sizeRemove,
    } = useFieldArray({
        control,
        name: 'sizes',
    })

    const onSubmit = (value: any, e: any) => {
        console.log('value ::', value)
        let hasError = false
        console.log(e?.target?.dataset?.close)
        if (thumbnailFiles?.length == 0) {
            setThumbnailError('Thumbnail image is required')
            hasError = true
            toast.push(
                <Notification
                    type="warning"
                    title="Thumbnail image is required"
                />,
                {
                    placement: 'top-center',
                }
            )
        } else setThumbnailError(null)

        // if (bannerFiles.length == 0) {
        //   setBannerError('Banner image is required')
        //   hasError = true;
        // } else setBannerError(null)

        if (productFiles?.length == 0) {
            setProductError('Product image is required')
            hasError = true
            toast.push(
                <Notification
                    type="warning"
                    title="Product image is required"
                />,
                {
                    placement: 'top-center',
                }
            )
        } else setProductError(null)

        // if (descriptionFiles?.length == 0) {
        //     setDescriptionError('Description image is required')
        //     hasError = true
        // } else setDescriptionError(null)

        if (hasError) return

        // const payload = {
        //     name: {
        //         en: value?.nameEn,
        //         ar: value?.nameAr,
        //     },
        //     brand: value?.brand?.value,
        //     weight: value?.weight,
        //     frameShape: value?.frameShape?.value,
        //     frameType: value?.frameType?.value,
        //     gender: value?.gender?.value,
        //     // plans: value.plans?.map((plan: any) => plan.value),
        //     category: value?.category?.map((category: any) => category.value),
        //     ageGroup: value?.ageGroup?.map((ageGroup: any) => ageGroup.value),
        //     recommendedProducts: value?.recommendedProducts?.map((product: any) => product.value),
        //     boughtTogether: value?.boughtTogether?.map((product: any) => product.value),
        //     sku: value?.sku,
        //     price: { aed: value?.priceAed },
        //     offerPrice: { aed: value?.offerPriceAed },
        //     stock: value?.stock,
        //     description: {
        //         en: value?.descriptionEn,
        //         ar: value?.descriptionAr,
        //     },
        //     descriptionTwo: {
        //         en: value?.descriptionTwoEn,
        //         ar: value?.descriptionTwoAr,
        //     },
        //     productType: 'frame',
        //     isNewArrival: value?.isNewArrival?.value,
        //     isVirtualTry: value?.isVirtualTry?.value,
        //     isTryCart: value?.isTryCart?.value,
        //     label: value?.label?.value,
        //     technicalInfo: value?.technicalInfo?.map((info: any) => ({
        //         title: {
        //             en: info?.titleEn,
        //             ar: info?.titleAr,
        //         },
        //         description: {
        //             en: info?.contentEn,
        //             ar: info?.contentAr,
        //         },
        //     })),
        //     color: value?.color?.value,
        //     sizes: value?.sizes?.map((size: any) => ({
        //         size: size?.value,
        //         stock: size?.stock,
        //         price: size?.price,
        //         offerPrice: size?.offerPrice
        //     })),
        //     thumbnail: thumbnailFiles[0],
        //     image: productFiles?.map((file: any) => file),
        //     description: descriptionFiles?.map((file: any) => file),
        //     isChooseLens: value.isChooseLens,
        // }

        const formData = new FormData()

        if (searchParams.get("parent")) {
            formData.append('parent', searchParams.get("parent") as string)
        }

        const name = {
            nameEn: value.nameEn,
            nameAr: value.nameAr,
        }
        formData.append('name[en]', name.nameEn)
        formData.append('name[ar]', name.nameAr)
        formData.append('brand', value.brand.value)
        formData.append('weight', value.weight)
        formData.append('position', value.position)
        formData.append('modelName', value.modelName)
        formData.append('modelCode', value.modelCode)
        formData.append('colorCode', value.colorCode)
        formData.append('slug', value.slug ?? "");
        if (value.frameShape.value)
            formData.append('frameShape', value.frameShape.value)
        if (value.frameType.value)
            formData.append('frameType', value.frameType.value)
        if (value?.gender) formData.append('gender', value?.gender?.value)
        // if (value.plans && value?.plans?.length > 0) {
        //   for (let i = 0; i < value.plans.length; i++) {
        //     formData.append('plans', value.plans[i].value);
        //   }
        // }

        if (value.parent) formData.append('parent', value.parent?.value)

        if (value?.isCashbackEnabled?.value) formData.append('isCashbackEnabled', value?.isCashbackEnabled?.value);
        formData.append('cashbackPercentage', value?.cashbackPercentage)

        for (let i = 0; i < value.category.length; i++) {
            formData.append('category', value.category[i].value)
        }
        formData.delete('subCategory')
        for (let i = 0; i < value?.subCategory?.length; i++) {
            formData.append('subCategory', value?.subCategory[i]?.value)
        }
        for (let i = 0; i < value?.subChildCategory?.length; i++) {
            formData.append('subCategory', value?.subChildCategory[i]?.value)
        }
        // for (let i = 0; i < value.ageGroup.length; i++) {
        //     formData.append('ageGroup', value.ageGroup[i].value)
        // }
        if (value.recommended) {
            for (let product of value.recommended) {
                formData.append('recommendedProducts', product.value)
            }
        }
        if (value.boughtTogether) {
            for (let product of value.boughtTogether) {
                formData.append('boughtTogether', product.value);
            }
        }
        if (value.bundleOfferProduct) {
            formData.append('bundleOfferProduct', value.bundleOfferProduct.value);
        }
        if (value.bundleDiscountPrice) {
            formData.append('bundleDiscountPrice', value.bundleDiscountPrice);
        }
        formData.append('sku', value.sku)

        const price = {
            aed: value.priceAed,
        }
        const offerPrice = {
            aed: value.offerPriceAed,
        }
        formData.append('price[aed]', price.aed)
        formData.append('offerPrice[aed]', offerPrice.aed)
        formData.append('stock', value.stock)

        formData.append('description[en]', value.descriptionEn)
        formData.append('description[ar]', value.descriptionAr)
        formData.append('descriptionTwo[en]', value.descriptionTwoEn)
        formData.append('descriptionTwo[ar]', value.descriptionTwoAr)
        formData.append('productType', 'frame')
        formData.append('isNewArrival', value.isNewArrival.value)
        // if (value.isReturnable.value) formData.append('isReturnable', value.isReturnable.value);
        if (value.isVirtualTry.value)
            formData.append('isVirtualTry', value.isVirtualTry.value)
        formData.append('isTryCart', value.isTryCart.value)
        // if (value?.returnDays) formData.append('returnDays', value?.returnDays);
        if (value?.label?.value) formData.append('label', value?.label?.value)
        else formData.append('label', "null")

        const technicalInfo = value.technicalInfo
        for (let i = 0; i < technicalInfo.length; i++) {
            formData.append(
                `technicalInfo[${i}][title][en]`,
                technicalInfo[i].titleEn
            )
            formData.append(
                `technicalInfo[${i}][title][ar]`,
                technicalInfo[i].titleAr
            )
            formData.append(
                `technicalInfo[${i}][description][en]`,
                technicalInfo[i].contentEn
            )
            formData.append(
                `technicalInfo[${i}][description][ar]`,
                technicalInfo[i].contentAr
            )
        }

        formData.append('color', value.color?.value)
        formData.append('size', value?.size?.value ?? null)

        // if (bannerFiles) formData.append('banner', bannerFiles[0]);
        if (thumbnailFiles) formData.append('thumbnail', thumbnailFiles[0]);
        // if (variantFiles) formData.append('variantImage', variantFiles[0]);
        if (productFiles) {
            for (let i = 0; i < productFiles.length; i++) {
                if (typeof productFiles[i] == "string") {
                    formData.append(`image`, productFiles[i])
                } else {
                    formData.append('images', productFiles[i])
                    formData.append(`image`, productFiles[i].name)
                }
            }
        }
        if (descriptionFiles) {
            for (let i = 0; i < descriptionFiles.length; i++) {
                if (typeof descriptionFiles[i] == "string") {
                    formData.append('descriptionImages', descriptionFiles[i])
                } else {
                    console.log(descriptionFiles[i])
                    formData.append('descriptionImages', descriptionFiles[i]?.name as string)
                    formData.append('descriptionFiles', descriptionFiles[i])
                }
            }
        }
        if (videoUrl) formData.append('video', videoUrl);
        // formData.append('variantDisplayType', variantType);
        formData.append('isChooseLens', isChooseLens ? 'true' : 'false');
        formData.append('isAddToCart', isAddToCart ? 'true' : 'false');
        formData.append('showDiscountPercentage', isShowPercentage ? 'true' : 'false')
        formData.append('isTaxIncluded', isTaxIncluded ? 'true' : 'false')
        formData.append('supplierSku', value.supplierSku)
        formData.append('upc', value.upc)

        formData.append('ogImage', ogImageFile[0])
        formData.append('seoDetails[title][en]', value.metaTitleEn)
        formData.append('seoDetails[title][ar]', value.metaTitleAr)
        formData.append('seoDetails[description][en]', value.metaDescriptionEn)
        formData.append('seoDetails[description][ar]', value.metaDescriptionAr)
        formData.append('seoDetails[keywords][en]', value.metaKeywordsEn)
        formData.append('seoDetails[keywords][ar]', value.metaKeywordsAr)
        formData.append('seoDetails[canonical][en]', value.metaCanonicalUrl)
        formData.append('seoDetails[canonical][ar]', value.metaCanonicalUrlAr)


        if (value?.frontMaterial?.length > 0) {
            for (let i = 0; i < value?.frontMaterial?.length; i++) {
                formData.append('frontMaterial', value?.frontMaterial[i]?.value)
            }
        }
        if (value?.type?.length > 0) {
            for (let i = 0; i < value?.type?.length; i++) {
                formData.append('type', value?.type[i]?.value)
            }
        }
        if (value?.lensMaterial?.length > 0) {
            for (let i = 0; i < value?.lensMaterial?.length; i++) {
                formData.append('lensMaterial', value?.lensMaterial[i]?.value)
            }
        }

        if (value?.sphValues?.length > 0) {
            for (let i = 0; i < value?.sphValues?.length; i++) {
                formData.append('sphValues', value?.sphValues[i]?.value)
            }
        }
        if (value?.cylValues?.length > 0) {
            for (let i = 0; i < value?.cylValues?.length; i++) {
                formData.append('cylValues', value?.cylValues[i]?.value)
            }
        }
        if (value?.axisValues?.length > 0) {
            for (let i = 0; i < value?.axisValues?.length; i++) {
                formData.append('axisValues', value?.axisValues[i]?.value)
            }
        }
        if (value?.addValues?.length > 0) {
            for (let i = 0; i < value?.addValues?.length; i++) {
                formData.append('addValues', value?.addValues[i]?.value)
            }
        }

        if (
            (params.id && params.type == 'Edit-Product') ||
            (params.id && params.type == 'Edit-Variant')
        ) {
            formData.append('refid', params.id)
            formData.append('isActive', value.isActive.value)

            api.post(endpoints.updateProduct, formData).then((res) => {
                if (res.status == 200) {
                    if (res?.data?.errorCode == 0) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )

                        if (e?.target?.dataset?.close == "true") {
                            navigate('/catalog/sunglasses')
                        }
                    }
                } else {
                    toast.push(
                        <Notification
                            type="warning"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            }).catch((err) => {
                console.log(err)
                toast.push(
                    <Notification
                        type="warning"
                        title={err?.response?.data?.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )

                if (err?.response?.data?.message == "Slug already exists") {
                    control.setError("slug", { message: "Slug already exist" }, { shouldFocus: true })
                }
            })
        } else if (params.type == 'Add-Product') {
            api.post(endpoints.createProduct, formData)
                .then((res: any) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            if (e?.target?.dataset?.close == "true") {
                                if (searchParams.get("redirect")) {
                                    navigate(searchParams.get("redirect") as string)
                                    return;
                                }
                                navigate('/catalog/sunglasses')
                            }
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message ?? res.response.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else if (params.type == 'Add-Variant' && params.id) {
            formData.append('isDefaultVariant', 'false')
            formData.append('mainProduct', productId)
            api.post(endpoints.createProduct, formData)
                .then((res) => {
                    console.log(res)
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            if (e?.target?.dataset?.close == "true") {
                                navigate('/catalog/sunglasses')
                            }
                        } else {
                            toast.push(
                                <Notification
                                    type="warning"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                        }
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    console.log(err)
                })
        }
    }

    const saveDuplicate = async () => {
        setLoading(true)
        const {slug, _id, createdAt, updatedAt, refid, parent, ...duplicateProduct} = product
        let data = {
            ...duplicateProduct,
            name: {
                en: product.name.en + '(Duplicate)',
                ar: product.name.ar + '(Duplicate)',
            },
            isActive: false,
        }

        api.post(endpoints.duplicateProduct, data).then((res) => {
            if (res.status == 200) {
                if (res?.data?.errorCode == 0) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/catalog/sunglasses')
                }
            } else {
                setLoading(false)
                toast.push(
                    <Notification
                        type="warning"
                        title={res.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }
        })
    }



    return (
        <>
            <h3 className="mb-2">{params.type?.replace(/-/g, ' ')}</h3>
            <Breadcrumb items={breadcrumbItems} />
            {params?.id && <Button
                className="w-fit my-3 ml-auto"
                variant="solid"
                type="button"
                icon={<AiOutlineSave />}
                onClick={saveDuplicate}
                loading={loading}
            >
                Save Duplicate
            </Button>}
            <form>
                <h5>Name</h5>
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="English">
                        <Controller
                            name="nameEn"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="text"
                                    onChange={(e) => {
                                        onNameChange(e)
                                        field.onChange(e)
                                    }}
                                    className={`${errors.nameEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        }`}
                                />
                            )}
                        />
                        {errors.nameEn && (
                            <small className="text-red-600 py-3">
                                {errors.nameEn.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Arabic">
                        <Controller
                            name="nameAr"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    dir="rtl"
                                    type="text"
                                    className={`${errors.nameAr
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        }`}
                                    {...field}
                                />
                            )}
                        />
                        {/* {errors.nameAr && <small className="text-red-600 py-3">{errors.nameAr.message as string}</small>} */}
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Brand">
                        <Controller
                            name="brand"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={brandOptions} {...field} />
                            )}
                        />
                        {errors.brand && (
                            <small className="text-red-600 py-3">
                                {errors.brand.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Category">
                        <Controller
                            name="category"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select
                                    isMulti
                                    options={categoryOptions}
                                    {...field}
                                />
                            )}
                        />
                        {errors.category && (
                            <small className="text-red-600 py-3">
                                {errors.category.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                {subCats && subCats.length > 0 && <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Sub Category">
                        <Controller
                            name="subCategory"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}

                            render={({ field }) => (
                                <Select
                                    isMulti
                                    options={subCats}
                                    {...field}
                                />
                            )}
                        />
                        {errors.subCategory && (
                            <small className="text-red-600 py-3">
                                {errors.subCategory.message as string}
                            </small>
                        )}
                    </FormItem>

                    {subChildCats && subChildCats?.length > 0 && <FormItem label="Third Category">
                        <Controller
                            name="subChildCategory"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select
                                    isMulti
                                    options={subChildCats}
                                    {...field}
                                />
                            )}
                        />
                        {errors.subChildCategory && (
                            <small className="text-red-600 py-3">
                                {errors.subChildCategory.message as string}
                            </small>
                        )}
                    </FormItem>}
                </div>}

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Position">
                        <Controller
                            name="position"
                            control={control}
                            defaultValue="0"
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input min={0} type="number" {...field} />
                            )}
                        />
                        {errors.position && (
                            <small className="text-red-600 py-3">
                                {errors.position.message as string}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Slug">
                        <Controller
                            name="slug"
                            control={control}
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input type="text" {...field} />
                            )}
                        />
                        {errors.slug && (
                            <small className="text-red-600 py-3">
                                {errors.slug.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Cashback">
                        <Controller
                            name="isCashbackEnabled"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select {...field} options={options} />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Cashback Percentage">
                        <Controller
                            name="cashbackPercentage"
                            control={control}
                            defaultValue="0"
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input min={0} type="number" {...field} />
                            )}
                        />
                        {errors.cashbackPercentage && (
                            <small className="text-red-600 py-3">
                                {errors.cashbackPercentage.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Supplier SKU">
                        <Controller
                            name="supplierSku"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.supplierSku && (
                            <small className="text-red-600 py-3">
                                {errors.supplierSku.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="UPC">
                        <Controller
                            name="upc"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.upc && (
                            <small className="text-red-600 py-3">
                                {errors.upc.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Model Name">
                        <Controller
                            name="modelName"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.modelName && (
                            <small className="text-red-600 py-3">
                                {errors.modelName.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Model Code">
                        <Controller
                            name="modelCode"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.modelCode && (
                            <small className="text-red-600 py-3">
                                {errors.modelCode.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Color Code">
                        <Controller
                            name="colorCode"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => <Input {...field} />}
                        />
                        {errors.colorCode && (
                            <small className="text-red-600 py-3">
                                {errors.colorCode.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div>
                    <FormItem label="Thumbnail">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={thumbnailFiles}
                            onChange={handleThumbnailUpload}
                            ratio={[500, 230]}
                        />
                        {thumbnailError && (
                            <small className="text-red-600">{thumbnailError}</small>
                        )}
                    </FormItem>

                    {/* <FormItem label="Banner">
          <Upload draggable uploadLimit={1} accept="image/*" fileList={bannerFiles} onChange={handleBannerUpload} />
          {bannerError && <small className="text-red-600">{bannerError}</small>}
        </FormItem> */}
                </div>

                <div>
                    <FormItem label="Product Images">
                        <Upload
                            draggable
                            multiple
                            accept="image/*"
                            fileList={productFilesPreview}
                            onChange={handleProductUpload}
                            onFileRemove={(files: any) => {
                                setProductFiles(files)
                            }}
                            ratio={[2560, 1800]}
                        />
                        {productError && (
                            <small className="text-red-600">{productError}</small>
                        )}
                    </FormItem>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="SKU">
                        <Controller
                            name="sku"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.sku
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        }`}
                                    {...field}
                                />
                            )}
                        />
                        {errors.sku && (
                            <small className="text-red-600 py-3">
                                {errors.sku.message as string}
                            </small>
                        )}
                    </FormItem>

                    {/* <FormItem label="Age Groups">
                    <Controller
                        name="ageGroup"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select isMulti options={ageOptions} {...field} />
                        )}
                    />
                </FormItem> */}

                    <FormItem label="Gender">
                        <Controller
                            name="gender"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select options={genderOptions} {...field} />
                            )}
                        />
                    </FormItem>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Product Head">
                        <Controller
                            name="parent"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select onInputChange={(value) => setProductHeadValue(value)} options={parentOptions} {...field} />
                            )}
                        />
                        {errors.brand && (
                            <small className="text-red-600 py-3">
                                {errors.brand.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>
                {/* <h4>Variant Type</h4>
            <div className="flex gap-4 mb-4 mt-2">
                <div className="flex gap-2 items-center">
                    <Radio checked={variantType === 'color'} onChange={() => setVariantType('color')} name='variantType' id="color" />
                    <label htmlFor="color">Color</label>
                </div>
                <div className="flex gap-2 items-center">
                    <Radio checked={variantType === 'image'} onChange={() => setVariantType('image')} name='variantType' id="image" />
                    <label htmlFor="image">Image</label>
                </div>
            </div> */}

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Colour">
                        <Controller
                            name="color"
                            control={control}
                            rules={{ required: 'Field is required' }}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    options={colorOptions}
                                    isOptionDisabled={(option) => option.isDisabled}
                                    {...field}
                                />
                            )}
                        />
                        {errors.color && (
                            <small className="text-red-600 py-3">
                                {errors.color.message as string}
                            </small>
                        )}
                    </FormItem>

                    {/* {variantType === 'image' && (
                    <FormItem label="Variant Image">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={variantFiles}
                            onChange={handleVariantUpload}
                            ratio={[500, 230]}
                        />
                        {variantError && (
                            <small className="text-red-600">{variantError}</small>
                        )}
                    </FormItem>
                )} */}

                    <FormItem label="Size">
                        <Controller
                            name="size"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    options={sizeOptions}
                                    {...field}
                                />
                            )}
                        />
                        {errors.size && (
                            <small className="text-red-600 py-3">
                                {errors.size.message as string}
                            </small>
                        )}
                    </FormItem>

                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="Stock">
                        <Controller
                            name="stock"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input min={0} type="number" {...field} />
                            )}
                        />
                        {errors.stock && (
                            <small className="text-red-600 py-3">
                                {errors.stock.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Price">
                        <Controller
                            name="priceAed"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    min={0}
                                    type="number"
                                    step="any"
                                    {...field}
                                />
                            )}
                        />
                        {errors.priceAed && (
                            <small className="text-red-600 py-3">
                                {errors.priceAed.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Offer Price">
                        <Controller
                            name="offerPriceAed"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Input
                                    min={0}
                                    type="number"
                                    step="any"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>


                <div className="my-4 font-semibold">
                    <Checkbox checked={isShowPercentage} onChange={onIsShowPercentage}>
                        Show Discount Percentage
                    </Checkbox>
                </div>


                {/* <div className="grid grid-cols-2 gap-4">
        <FormItem label="Price">
          <Controller
            name="priceQr"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Input
                prefix={<strong>QR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.priceQr && <small className="text-red-600 py-3">{errors.priceQr.message as string}</small>}
        </FormItem>

        <FormItem label="Offer Price">
          <Controller
            name="offerPriceQr"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <Input
                prefix={<strong>QR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.offerPriceAed && <small className="text-red-600 py-3">{errors.offerPriceAed.message as string}</small>}
        </FormItem>
      </div> */}

                {/* <div className="grid grid-cols-2 gap-4">
        <FormItem label="Price">
          <Controller
            name="priceSar"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Input
                prefix={<strong>SAR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.priceSar && <small className="text-red-600 py-3">{errors.priceSar.message as string}</small>}
        </FormItem>

        <FormItem label="Offer Price">
          <Controller
            name="offerPriceSar"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <Input
                prefix={<strong>SAR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.offerPriceSar && <small className="text-red-600 py-3">{errors.offerPriceSar.message as string}</small>}
        </FormItem>
      </div> */}

                {/* <div className="grid grid-cols-2 gap-4">
        <FormItem label="Price">
          <Controller
            name="priceOmr"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Input
                prefix={<strong>OMR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.priceOmr && <small className="text-red-600 py-3">{errors.priceOmr.message as string}</small>}
        </FormItem>

        <FormItem label="Offer Price">
          <Controller
            name="offerPriceOmr"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <Input
                prefix={<strong>OMR</strong>}
                type="number"
                {...field}
              />
            )}
          />
          {errors.offerPriceOmr && <small className="text-red-600 py-3">{errors.offerPriceOmr.message as string}</small>}
        </FormItem>
      </div> */}

                <div>
                    <FormItem label="Description ( English )">
                        <Controller
                            name="descriptionEn"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            control={control}
                            render={({ field }) => (
                                <RichTextEditor {...field} modules={'image'} />
                            )}
                        />
                        {errors.descriptionEn && (
                            <small className="text-red-600 py-3">
                                {errors.descriptionEn.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div>
                    <FormItem label="Description ( Arabic )">
                        <Controller
                            name="descriptionAr"
                            defaultValue=""
                            control={control}
                            render={({ field }) => (
                                <RichTextEditor {...field} modules={'image'} />
                            )}
                        />
                    </FormItem>
                </div>

                <div>
                    <FormItem label="Description two ( English )">
                        <Controller
                            name="descriptionTwoEn"
                            defaultValue=""
                            control={control}
                            render={({ field }) => (
                                <RichTextEditor {...field} modules={'image'} />
                            )}
                        />
                    </FormItem>
                </div>

                <div>
                    <FormItem label="Description two ( Arabic )">
                        <Controller
                            name="descriptionTwoAr"
                            defaultValue=""
                            control={control}
                            render={({ field }) => (
                                <RichTextEditor {...field} modules={'image'} />
                            )}
                        />
                    </FormItem>
                </div>

                <Button
                    className="mt-6"
                    variant="solid"
                    type="button"
                    icon={<AiOutlinePlus />}
                    onClick={() => {
                        append({})
                    }}
                >
                    Add Technical Information
                </Button>

                <ul className="mt-6">
                    {fields.map((item, index) => {
                        return (
                            <li key={item.id}>
                                <div className="flex  space-x-2">
                                    <FormContainer layout="inline">
                                        <FormItem label="Title(English)">
                                            <Controller
                                                defaultValue={''}
                                                render={({ field }) => (
                                                    <input
                                                        type="text"
                                                        className={`${errors.technicalInfo
                                                            ? ' input input-md h-11 input-invalid'
                                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                            }`}
                                                        {...field}
                                                    />
                                                )}
                                                name={`technicalInfo.${index}.titleEn`}
                                                control={control}
                                            />
                                        </FormItem>

                                        <FormItem label="Content(English)">
                                            <Controller
                                                defaultValue={''}
                                                render={({ field }) => (
                                                    <input
                                                        type="text"
                                                        className={`${errors.technicalInfo
                                                            ? ' input input-md h-11 input-invalid'
                                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                            }`}
                                                        {...field}
                                                    />
                                                )}
                                                name={`technicalInfo.${index}.contentEn`}
                                                control={control}
                                            />
                                        </FormItem>

                                        <FormItem label="Title(Arabic)">
                                            <Controller
                                                defaultValue={''}
                                                render={({ field }) => (
                                                    <input
                                                        dir="rtl"
                                                        type="text"
                                                        className={`${errors.technicalInfo
                                                            ? ' input input-md h-11 input-invalid'
                                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                            }`}
                                                        {...field}
                                                    />
                                                )}
                                                name={`technicalInfo.${index}.titleAr`}
                                                control={control}
                                            />
                                        </FormItem>

                                        <FormItem label="Content(Arabic)">
                                            <Controller
                                                defaultValue={''}
                                                render={({ field }) => (
                                                    <input
                                                        dir="rtl"
                                                        type="text"
                                                        className={`${errors.technicalInfo
                                                            ? ' input input-md h-11 input-invalid'
                                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                            }`}
                                                        {...field}
                                                    />
                                                )}
                                                name={`technicalInfo.${index}.contentAr`}
                                                control={control}
                                            />
                                        </FormItem>
                                    </FormContainer>

                                    <Button
                                        size="sm"
                                        shape="circle"
                                        type="button"
                                        icon={<AiOutlineMinus />}
                                        onClick={() => remove(index)}
                                    ></Button>
                                </div>
                            </li>
                        )
                    })}
                </ul>

                <div>
                    <FormItem label="Description Images">
                        <Upload
                            draggable
                            multiple
                            accept="image/*"
                            fileList={descriptionFiles}
                            onChange={handleDescriptionUpload}
                            ratio={[1080, 1060]}
                            onFileRemove={handleDescriptionUpload}
                        />
                        {descriptionError && (
                            <small className="text-red-600">
                                {descriptionError}
                            </small>
                        )}
                    </FormItem>
                </div>

                <p className="font-bold font-gray-700">Video Upload</p>
                <div className="flex flex-col items-center justify-center border border-dashed border-gray-400 p-4 mb-4">
                    <label
                        htmlFor="upload-button"
                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full cursor-pointer"
                    >
                        Select Video
                    </label>
                    <input
                        type="file"
                        id="upload-button"
                        className="hidden"
                        accept="video/mp4,video/x-m4v,video/*"
                        onChange={handleFileChange}
                    // value={videoFile}
                    />
                    {videoPreview && (
                        <div key={videoPreview} className="mt-4">
                            <video
                                width="320"
                                height="240"
                                controls
                                className="rounded-lg shadow-lg"
                            >
                                <source src={videoPreview} type="video/mp4" />
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    )}
                    {videoFile && (
                        <button
                            type="button"
                            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-full mt-4"
                            onClick={() => handleVideoUpload()}
                        >
                            Upload
                        </button>
                    )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Frame Shape">
                        <Controller
                            name="frameShape"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={frameShapeOptions} {...field} />
                            )}
                        />
                        {/* {errors.frameShape && (
                        <small className="text-red-600 py-3">
                            {errors.frameShape.message as string}
                        </small>
                    )} */}
                    </FormItem>

                    <FormItem label="Frame Type">
                        <Controller
                            name="frameType"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={frameTypeOptions} {...field} />
                            )}
                        />
                        {/* {errors.frameType && (
                        <small className="text-red-600 py-3">
                            {errors.frameType.message as string}
                        </small>
                    )} */}
                    </FormItem>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="Front Material">
                        <Controller
                            name="frontMaterial"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select isMulti options={frontMaterialOptions} {...field} />
                            )}
                        />
                        {/* {errors.frontMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.frontMaterial.message as string}
                        </small>
                    )} */}
                    </FormItem>

                    <FormItem label="Type">
                        <Controller
                            name="type"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select isMulti options={typeOptions} {...field} />
                            )}
                        />
                        {/* {errors.type && (
                        <small className="text-red-600 py-3">
                            {errors.type.message as string}
                        </small>
                    )} */}
                    </FormItem>

                    <FormItem label="Lens Material">
                        <Controller
                            name="lensMaterial"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select isMulti options={lensMaterialOptions} {...field} />
                            )}
                        />
                        {/* {errors.lensMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.lensMaterial.message as string}
                        </small>
                    )} */}
                    </FormItem>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="Size (bridge)">
                        <Controller
                            name="sizeBridge"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input {...field} />
                            )}
                        />
                        {/* {errors.frontMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.frontMaterial.message as string}
                        </small>
                    )} */}
                    </FormItem>

                    <FormItem label="Temple Length">
                        <Controller
                            name="templeLength"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input {...field} />
                            )}
                        />
                        {/* {errors.type && (
                        <small className="text-red-600 py-3">
                            {errors.type.message as string}
                        </small>
                    )} */}
                    </FormItem>

                    <FormItem label="Lens Color">
                        <Controller
                            name="lenseColor"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input {...field} />
                            )}
                        />
                        {/* {errors.lensMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.lensMaterial.message as string}
                        </small>
                    )} */}
                    </FormItem>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <FormItem label="Product Weight (gm)">
                        <Controller
                            name="weight"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="number"
                                    className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                    {...field}
                                />
                            )}
                        />
                        {errors.weight && (
                            <small className="text-red-600 py-3">
                                {errors.weight.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Recommended Products">
                        <Controller
                            name="recommended"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    isMulti
                                    options={productOptions}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem label="Bought Together">
                        <Controller
                            name="boughtTogether"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    isMulti
                                    options={productOptions}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem label="Bundle Offer Product">
                        <Controller
                            name="bundleOfferProduct"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    options={productOptions}
                                    placeholder="Select a product for bundle offer"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem label="Bundle Discount Price (AED)">
                        <Controller
                            name="bundleDiscountPrice"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Input
                                    type="number"
                                    placeholder="Enter discount price for bundle"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4 ">
                    <FormItem label="is New Arrival? ">
                        <Controller
                            name="isNewArrival"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                        {errors.isNewArrival && (
                            <small className="text-red-600 py-3">
                                {errors.isNewArrival.message as string}
                            </small>
                        )}
                    </FormItem>

                    {/* <FormItem label="is Returnable? ">
          <Controller
            name="isReturnable"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Select
                options={options}
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  if (e.value == 'true') {
                    setIsReturn(true);
                  } else {
                    setIsReturn(false);
                  }
                }}
              />
            )}
          />
          {errors.isReturnable && <small className="text-red-600 py-3">{errors.isReturnable.message as string}</small>}
        </FormItem> */}

                    {/* {isReturn && (
          <FormItem label="Return Days">
            <Controller
              name="returnDays"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Input
                  type="number"
                  min={0}
                  {...field}
                />
              )}
            />
          </FormItem>
        )} */}

                    <FormItem label="Labels">
                        <Controller
                            name="label"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    options={labels}
                                    isSearchable={false}
                                    {...field}
                                    onChange={(e) => {
                                        if (field?.value?.value === e?.value) {
                                            setValue("label", null)
                                        } else {
                                            field.onChange(e)
                                        }
                                    }}
                                />
                            )}
                        />
                    </FormItem>
                </div>

                <div className="grid grid-cols-3 gap-4 ">
                    <FormItem label="Virtual Try Available? ">
                        <Controller
                            name="isVirtualTry"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                        {errors.isVirtualTry && (
                            <small className="text-red-600 py-3">
                                {errors.isVirtualTry.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Try cart Available ? ">
                        <Controller
                            name="isTryCart"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                        {errors.isTryCart && (
                            <small className="text-red-600 py-3">
                                {errors.isTryCart.message as string}
                            </small>
                        )}
                    </FormItem>

                    {params.id && (
                        <FormItem label="is Active ? ">
                            <Controller
                                name="isActive"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <Select options={options} {...field} />
                                )}
                            />
                            {errors.isActive && (
                                <small className="text-red-600 py-3">
                                    {errors.isActive.message as string}
                                </small>
                            )}
                        </FormItem>
                    )}
                </div>

                <div className="my-4 font-semibold">
                    <Checkbox checked={isChooseLens} onChange={onIsChooseLensCheck}>
                        Choose Lens
                    </Checkbox>
                    <Checkbox checked={isAddToCart} onChange={onIsAddToCartCheck}>
                        Add To Cart
                    </Checkbox>
                    <Checkbox checked={isTaxIncluded} onChange={onIsTaxIncluded}>
                        is Tax Included
                    </Checkbox>
                </div>

                {
                    isChooseLens && <div className="grid grid-cols-4 gap-4">
                        <FormItem label="SPH Values">
                            <Controller
                                name="sphValues"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Select isMulti options={sphValues} {...field} />
                                )}
                            />
                            {/* {errors.frontMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.frontMaterial.message as string}
                        </small>
                    )} */}
                        </FormItem>

                        <FormItem label="CYL Values">
                            <Controller
                                name="cylValues"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Select isMulti options={cylValues} {...field} />
                                )}
                            />
                            {/* {errors.type && (
                        <small className="text-red-600 py-3">
                            {errors.type.message as string}
                        </small>
                    )} */}
                        </FormItem>

                        <FormItem label="AXIS Values">
                            <Controller
                                name="axisValues"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Select isMulti options={axisValues} {...field} />
                                )}
                            />
                            {/* {errors.lensMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.lensMaterial.message as string}
                        </small>
                    )} */}
                        </FormItem>
                        <FormItem label="ADD Values">
                            <Controller
                                name="addValues"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Select isMulti options={addValues} {...field} />
                                )}
                            />
                            {/* {errors.lensMaterial && (
                        <small className="text-red-600 py-3">
                            {errors.lensMaterial.message as string}
                        </small>
                    )} */}
                        </FormItem>
                    </div>
                }


                <h5 className="mt-10">Seo Section</h5>
                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Meta Title English">
                        <Controller
                            name="metaTitleEn"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaTitleEn && (
                            <small className="text-red-600 py-3">
                                {errors.metaTitleEn.message as string}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Meta Title Arabic">
                        <Controller
                            name="metaTitleAr"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    dir="rtl"
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaTitleAr && (
                            <small className="text-red-600 py-3">
                                {errors.metaTitleAr.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Meta Description English">
                        <Controller
                            name="metaDescriptionEn"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaDescriptionEn && (
                            <small className="text-red-600 py-3">
                                {errors.metaDescriptionEn.message as string}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Meta Description Arabic">
                        <Controller
                            name="metaDescriptionAr"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    dir="rtl"
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaDescriptionAr && (
                            <small className="text-red-600 py-3">
                                {errors.metaDescriptionAr.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Meta Keywords English">
                        <Controller
                            name="metaKeywordsEn"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaKeywordsEn && (
                            <small className="text-red-600 py-3">
                                {errors.metaKeywordsEn.message as string}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Meta Keywords Arabic">
                        <Controller
                            name="metaKeywordsAr"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    dir="rtl"
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaKeywordsAr && (
                            <small className="text-red-600 py-3">
                                {errors.metaKeywordsAr.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Meta Canonical URL English">
                        <Controller
                            name="metaCanonicalUrl"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaCanonicalUrl && (
                            <small className="text-red-600 py-3">
                                {errors.metaCanonicalUrl.message as string}
                            </small>
                        )}
                    </FormItem>

                    <FormItem label="Meta Canonical URL Arabic">
                        <Controller
                            name="metaCanonicalUrlAr"
                            control={control}
                            defaultValue=""
                            // rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    {...field}
                                />
                            )}
                        />
                        {errors.metaCanonicalUrlAr && (
                            <small className="text-red-600 py-3">
                                {errors.metaCanonicalUrlAr.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                    <FormItem label="Meta OG Image">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={ogImageFile}
                            onChange={handleOgImageUpload}
                            ratio={[1126, 1200]}
                        />
                    </FormItem>

                </div>

                <Button
                    className="float-right mt-6 mx-4 [&_*]:pointer-events-none"
                    variant="solid"
                    type="button"
                    data-close={true}
                    onClick={handleSubmit(onSubmit)}
                    icon={<AiOutlineSave />}
                >
                    Save & close
                </Button>
                <Button
                    className="float-right mt-6 [&_*]:pointer-events-none"
                    variant="solid"
                    type="button"
                    data-close={false}
                    onClick={handleSubmit(onSubmit)}
                    icon={<AiOutlineSave />}
                >
                    Save & Continue
                </Button>
            </form >
        </>
    )
}
